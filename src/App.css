body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background-color: #f0f2f5;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.container {
    max-width: 800px;
    width: 100%;
}

header {
    text-align: center;
    margin-bottom: 40px;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #1c1e21;
}

main {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
}

h2 {
    font-size: 1.5rem;
    margin-top: 0;
    margin-bottom: 20px;
}

.progress-bar-container {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    width: 100%;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, #4caf50, #81c784);
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease-in-out;
}

.percentage-text {
    font-size: 1.2rem;
    font-weight: bold;
    margin-top: 15px;
}

.large-text {
    font-size: 3.5rem;
    font-weight: bold;
    margin: 0;
    color: #4CAF50;
}