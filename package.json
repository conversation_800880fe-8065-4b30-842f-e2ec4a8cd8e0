{"name": "dls2026", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.7", "@supabase/supabase-js": "^2.57.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.8.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "3.4"}}